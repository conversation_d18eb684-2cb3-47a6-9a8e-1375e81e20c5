trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • Terraform Tests

# variables:
#   - group: 'TEST'
#   - group: "Azure Policy"  # Assuming NOTIFICATION_URL is here

jobs:
- job: Control
  timeoutInMinutes: 120
  pool:
    name: DEV-AksPool-centralagent-Deploy
  steps:
    - checkout: none
    - task: AzureCLI@2
      displayName: Control terraform test pipelines
      env:
        AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
        # AZURE_DEVOPS_EXT_PAT: $(TEST_PAT)
      inputs:
        azureSubscription: DEV-OTP-DD-COEINFDEV-sub-dev-01
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          az devops configure --defaults organization=https://dev.azure.com/ADOS-OTPHU-01
          az devops configure --defaults project=OTPHU-COE-TEMPLATESPEC
          az devops configure --list
          echo "PAT='${AZURE_DEVOPS_EXT_PAT:-<EMPTY>}'"

          # Get relevant repos
          # repos=$(az repos list -o json | jq -r '.[] | select(.name | startswith("terraform-azurerm-lo")) | .name')
          repos=$(az repos list --query [].name -o tsv)
          echo -e "Relevant repos: \n$repos"

          # # Get all pipelines
          # all_pipelines=$(az pipelines list --query [].name -o tsv | grep -E "erraformtest.TST|erraformtest.PRD")
          # echo -e "Relevant pipelines: \n$all_pipelines"

          # declare -A repo_tags repo_pipelines
          # declare -A build_ids pids result_files

          # result_dir=$(mktemp -d)
          # max_parallel_jobs=2
          # active_repos=0

          # function wait_for_jobs {
          #   sleep 15s
          #   while [ $(jobs -rp | wc -l) -ge $max_parallel_jobs ]; do
          #     sleep 15s
          #   done
          # }

          # # Collect tags and pipelines per repo
          # for repo in $repos; do
          #   tags=$(az repos ref list --repository "$repo" --filter tags --query [].name -o tsv | grep -E 'v[1-9]+\.[0-9]+\.[0-9]+$' | grep -v '-' | sed 's|^refs/tags/||')
          #   repo_tags["$repo"]="$tags"

          #   pipelines=$(echo "$all_pipelines" | grep -F "$repo")
          #   repo_pipelines["$repo"]="$pipelines"
          # done

          # # Function to process one repo serially
          # function process_repo {
          #   local repo=$1
          #   local tags="${repo_tags[$repo]}"
          #   local pipelines="${repo_pipelines[$repo]}"

          #   for tag in $tags; do
          #     # For each tag, launch pipelines in parallel
          #     for pipeline in $pipelines; do
          #       # Wait if overall max reached (though per repo serial)
          #       wait_for_jobs

          #       build_id=$(az pipelines run --name "$pipeline" --branch "$tag" --query "id" --output tsv)
          #       key="${repo}_${tag}_${pipeline}"
          #       build_ids[$key]=$build_id
          #       echo "Started $key: $build_id"

          #       result_file="$result_dir/$key.result"
          #       result_files[$key]=$result_file

          #       status=""
          #       while [[ $status != "completed" ]]; do
          #         sleep 30s
          #         status=$(az pipelines runs show --id "$build_id" --query "status" --output tsv)
          #       done
          #       result=$(az pipelines runs show --id "$build_id" --query "result" --output tsv)
          #       echo $result > $result_file
          #     done
          #   done
          # }

          # # Launch repo processing in parallel, up to max
          # for repo in $repos; do
          #   wait_for_jobs
          #   process_repo "$repo" &
          #   pids_repo[$repo]=$!
          # done

          # # Wait for all
          # for repo in $repos; do
          #   wait ${pids_repo[$repo]}
          # done

          # # Collect results
          # echo "Results:" > $(Pipeline.Workspace)/report.md
          # retVal=0
          # for key in "${!result_files[@]}"; do
          #   result=$(cat "${result_files[$key]}")
          #   echo "$key: $result" >> $(Pipeline.Workspace)/report.md
          #   if [ "$result" != "succeeded" ]; do
          #     echo "##vso[task.logissue type=error]$key: $result"
          #     retVal=1
          #   fi
          # done

          # rm -rf $result_dir
          # exit $retVal
    # - task: PublishPipelineArtifact@1
    #   displayName: Publish report.md artifact
    #   inputs:
    #     targetPath: '$(Pipeline.Workspace)/report.md'
    #     artifact: 'report'
    #     publishLocation: 'pipeline'
    # - task: AzureCLI@2
    #   displayName: Send notification
    #   condition: succeeded()
    #   env:
    #     AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
    #   inputs:
    #     azureSubscription: DEV-OTP-MGM-SharedSvc-sub-prd-01
    #     scriptType: bash
    #     scriptLocation: inlineScript
    #     inlineScript: |
    #       file=$(base64 $(Pipeline.Workspace)/report.md)
    #       url="$NOTIFICATION_URL"
    #       curl -k -H "Content-Type: text/plain" -d "$file" "$url"
