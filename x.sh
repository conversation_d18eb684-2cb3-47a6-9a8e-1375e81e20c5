#!/usr/bin/env bash
set -euo pipefail

declare -A repo_tags repo_pipelines

repos=$(az repos list -o json | jq -r '.[] | select(.name | startswith("terraform-azurerm-lo")) | .name')
all_pipelines=$(az pipelines list --query [].name -o tsv | grep -E "erraformtest.TST|erraformtest.PRD")

while IFS= read -r repo; do
    [[ -z "$repo" ]] && continue
    tags=$(az repos ref list --repository "$repo" --filter tags --query [].name -o tsv \
        | grep -E 'v[1-9]+\.[0-9]+\.[0-9]+$' | grep -v '-' | sed 's|^refs/tags/||' || true)
    repo_tags["$repo"]="$tags"

    pipelines=$(echo "$all_pipelines" | grep -F "$repo" || true)
    repo_pipelines["$repo"]="$pipelines"
done <<< "$repos"

echo "Repos with tags and pipelines:"
while IFS= read -r repo; do
    [[ -z "$repo" ]] && continue
    echo "Repo: $repo"

    tags="${repo_tags[$repo]:-}"
    if [[ -n "$tags" ]]; then
        echo "  Tags:"
        while IFS= read -r tag; do
            [[ -z "$tag" ]] && continue
            echo "    $tag"
        done <<< "$tags"
    else
        echo "  Tags: (none)"
    fi

    pipelines="${repo_pipelines[$repo]:-}"
    if [[ -n "$pipelines" ]]; then
        echo "  Pipelines:"
        while IFS= read -r pl; do
            [[ -z "$pl" ]] && continue
            echo "    $pl"
        done <<< "$pipelines"
    else
        echo "  Pipelines: (none)"
    fi
done <<< "$repos"